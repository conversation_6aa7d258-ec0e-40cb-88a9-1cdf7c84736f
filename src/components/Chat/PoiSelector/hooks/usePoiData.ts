import { usePagination } from 'ahooks';
import { getPoiListByPage } from '../api/poiApi';

/**
 * 自定义 Hook：管理 POI 数据获取和分页
 * @param searchValue 搜索关键词
 * @returns 数据和分页控制器
 */
export const usePoiData = (searchValue: string) => {
    const { data, pagination, loading } = usePagination(
        async ({ current, pageSize = 10 }) => {
            const resData = await getPoiListByPage(
                current,
                pageSize,
                searchValue,
            );
            return resData
                ? {
                      list: [
                          ...(current === 1 ? [] : data?.list || []),
                          ...resData.list,
                      ],
                      total: resData.total,
                  }
                : data;
        },
    );

    return {
        data,
        pagination,
        poiList: data?.list,
        loading,
    };
};
