import React from 'react';
import { View, Text, Image } from '@mrn/react-native';
import TWS from '../../../../TWS';
import noContentImg from '../../../../assets/images/noContent.png';

/**
 * 空列表组件
 */
export const ListEmptyComponent = () => (
    <View style={[TWS.center(), { marginTop: 100 }]}>
        <Image source={noContentImg} style={[TWS.square(120)]} />
        <Text
            style={{
                marginTop: 12,
                color: '#999',
                fontSize: 14,
            }}
        >
            仅支持查询您名下的商家
        </Text>
        <Text
            style={{
                color: '#999',
                fontSize: 14,
            }}
        >
            请确保输入信息无误
        </Text>
    </View>
);
